{"permissions": {"allow": ["mcp__filesystem__list_directory_with_sizes", "Bash(npx:*)", "Bash(npm install)", "Bash(docker-compose logs:*)", "<PERSON><PERSON>(docker-compose restart:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(python:*)", "Bash(./venv/bin/pip:*)", "mcp__filesystem__read_file", "mcp__filesystem__list_directory", "mcp__filesystem__search_files", "mcp__filesystem__edit_file", "<PERSON><PERSON>(source:*)", "Bash(new_venv/bin/python:*)"], "deny": []}}