from typing import Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime

class DocumentBase(BaseModel):
    title: str
    content: str
    template_type: str
    metadata_json: Optional[Dict[str, Any]] = None
    file_path: Optional[str] = None

class DocumentCreate(DocumentBase):
    pass

class DocumentUpdate(BaseModel):
    title: Optional[str] = None
    content: Optional[str] = None
    metadata_json: Optional[Dict[str, Any]] = None
    file_path: Optional[str] = None

class Document(DocumentBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True