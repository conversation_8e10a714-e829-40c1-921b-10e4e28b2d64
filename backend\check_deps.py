#!/usr/bin/env python3
"""
Check all required dependencies for the backend
"""
import sys
import importlib

# List of all required packages
required_packages = [
    'fastapi',
    'uvicorn',
    'sqlalchemy',
    'psycopg2',
    'redis',
    'python_multipart',
    'jose',
    'passlib',
    'pydantic',
    'pydantic_settings',
    'email_validator',
    'langchain',
    'alembic',
    'dateutil'
]

print("Checking dependencies...")
print("=" * 50)

missing = []
installed = []

for package in required_packages:
    try:
        if package == 'python_multipart':
            importlib.import_module('multipart')
        elif package == 'jose':
            importlib.import_module('jose')
        elif package == 'passlib':
            importlib.import_module('passlib')
        elif package == 'pydantic_settings':
            importlib.import_module('pydantic_settings')
        elif package == 'email_validator':
            importlib.import_module('email_validator')
        elif package == 'dateutil':
            importlib.import_module('dateutil')
        elif package == 'psycopg2':
            importlib.import_module('psycopg2')
        else:
            importlib.import_module(package)
        
        installed.append(package)
        print(f"✓ {package}")
    except ImportError as e:
        missing.append(package)
        print(f"✗ {package} - {e}")

print("=" * 50)
print(f"Installed: {len(installed)}")
print(f"Missing: {len(missing)}")

if missing:
    print("\nMissing packages:")
    for pkg in missing:
        print(f"  - {pkg}")
    
    print("\nTo install missing packages:")
    print("  pip install " + " ".join(missing))
else:
    print("\nAll dependencies are installed!")