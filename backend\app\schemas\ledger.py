from typing import Optional, Dict, Any
from pydantic import BaseModel
from datetime import datetime

class LedgerEntryBase(BaseModel):
    title: str
    description: Optional[str] = None
    category: str
    status: Optional[str] = "pending"
    assigned_to: Optional[str] = None
    priority: Optional[str] = "medium"
    metadata_json: Optional[Dict[str, Any]] = None

class LedgerEntryCreate(LedgerEntryBase):
    pass

class LedgerEntryUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    status: Optional[str] = None
    assigned_to: Optional[str] = None
    priority: Optional[str] = None
    metadata_json: Optional[Dict[str, Any]] = None

class LedgerEntry(LedgerEntryBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True